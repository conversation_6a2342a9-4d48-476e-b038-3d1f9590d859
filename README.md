# Dashboard - Mac Menu Bar App

A customizable Mac Menu Bar application that displays configurable panels either floating above all windows or positioned on the desktop.

## Features

### Panel Display Options
- **Highest Z-Index**: Display panels above all other windows including the menu bar
- **Desktop Mode**: Display panels behind all windows (on wallpaper)
- **Floating Mode**: Display panels above normal windows but below menu bar
- **Multiple positioning options**: Top-left, top-right, bottom-left, bottom-right, center, or custom positioning

### Customizable Content
- **Date & Time**: Live updating current time and date
- **System Information**: macOS version and memory information
- **Custom Text**: Add your own custom text content
- **Configurable appearance**: Title, size, background color, and opacity

### Settings & Configuration
- **Comprehensive Settings Dialog**: Access via menu bar or gear icon in panel
- **Appearance Settings**: Customize title, dimensions, colors, and opacity
- **Position Settings**: Choose panel position and window level
- **Content Settings**: Toggle different content types and add custom text
- **Behavior Settings**: Configure window behavior like always-on-top, hiding, and multi-space support

### Window Behavior
- **Always on Top**: Keep panels above other windows
- **Hide on Deactivate**: Automatically hide when clicking outside the app
- **Multi-Space Support**: Show panels on all virtual desktops
- **Live Updates**: Settings changes apply immediately

## Usage

1. **Launch the App**: The app runs as a menu bar application (no dock icon)
2. **Access Menu**: Click the chart bar icon in the menu bar
3. **Show/Hide Panels**: Use menu options or the panel will show automatically
4. **Configure Settings**: Click "Settings..." in the menu or the gear icon in the panel
5. **Customize**: Adjust appearance, position, content, and behavior to your preferences

## Menu Options
- **Show Panel**: Display the dashboard panel
- **Hide Panel**: Hide the dashboard panel
- **Refresh Panel**: Refresh panel with current settings
- **Settings...**: Open the settings dialog (⌘,)
- **Quit**: Exit the application (⌘Q)

## Settings Tabs

### Appearance
- Panel title customization
- Width and height sliders (200-800px width, 150-600px height)
- Opacity control (30-100%)
- Background color picker

### Position
- Position presets (corners, center, custom)
- Window level selection (desktop to highest z-index)
- Custom offset controls for precise positioning

### Content
- Toggle date & time display
- Toggle system information display
- Toggle and edit custom text content

### Behavior
- Always on top setting
- Hide on deactivate option
- Show on all spaces setting

## Technical Details
- Built with SwiftUI and AppKit
- Uses NSPanel for window management
- Supports multiple window levels including desktop and above menu bar
- Real-time settings synchronization using @AppStorage
- Menu bar only application (LSUIElement = true)

## Requirements
- macOS 13.0 or later
- Xcode 15.0 or later for building from source

## Building
1. Open `Dashboard/Dashboard.xcodeproj` in Xcode
2. Select the Dashboard scheme
3. Build and run (⌘R)

The app will appear in your menu bar with a chart icon.
