// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		9F7DB5432E37DFDE00782FDA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9F7DB52C2E37DFDC00782FDA /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9F7DB5332E37DFDC00782FDA;
			remoteInfo = Dashboard;
		};
		9F7DB54D2E37DFDE00782FDA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9F7DB52C2E37DFDC00782FDA /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 9F7DB5332E37DFDC00782FDA;
			remoteInfo = Dashboard;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		9F7DB5342E37DFDC00782FDA /* Dashboard.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Dashboard.app; sourceTree = BUILT_PRODUCTS_DIR; };
		9F7DB5422E37DFDE00782FDA /* DashboardTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = DashboardTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		9F7DB54C2E37DFDE00782FDA /* DashboardUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = DashboardUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		9F7DB5602E37E60E00782FDA /* Exceptions for "Dashboard" folder in "Dashboard" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 9F7DB5332E37DFDC00782FDA /* Dashboard */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		9F7DB5362E37DFDC00782FDA /* Dashboard */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				9F7DB5602E37E60E00782FDA /* Exceptions for "Dashboard" folder in "Dashboard" target */,
			);
			path = Dashboard;
			sourceTree = "<group>";
		};
		9F7DB5452E37DFDE00782FDA /* DashboardTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = DashboardTests;
			sourceTree = "<group>";
		};
		9F7DB54F2E37DFDE00782FDA /* DashboardUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = DashboardUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		9F7DB5312E37DFDC00782FDA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9F7DB53F2E37DFDE00782FDA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9F7DB5492E37DFDE00782FDA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		9F7DB52B2E37DFDC00782FDA = {
			isa = PBXGroup;
			children = (
				9F7DB5362E37DFDC00782FDA /* Dashboard */,
				9F7DB5452E37DFDE00782FDA /* DashboardTests */,
				9F7DB54F2E37DFDE00782FDA /* DashboardUITests */,
				9F7DB5352E37DFDC00782FDA /* Products */,
			);
			sourceTree = "<group>";
		};
		9F7DB5352E37DFDC00782FDA /* Products */ = {
			isa = PBXGroup;
			children = (
				9F7DB5342E37DFDC00782FDA /* Dashboard.app */,
				9F7DB5422E37DFDE00782FDA /* DashboardTests.xctest */,
				9F7DB54C2E37DFDE00782FDA /* DashboardUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		9F7DB5332E37DFDC00782FDA /* Dashboard */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9F7DB5562E37DFDE00782FDA /* Build configuration list for PBXNativeTarget "Dashboard" */;
			buildPhases = (
				9F7DB5302E37DFDC00782FDA /* Sources */,
				9F7DB5312E37DFDC00782FDA /* Frameworks */,
				9F7DB5322E37DFDC00782FDA /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				9F7DB5362E37DFDC00782FDA /* Dashboard */,
			);
			name = Dashboard;
			packageProductDependencies = (
			);
			productName = Dashboard;
			productReference = 9F7DB5342E37DFDC00782FDA /* Dashboard.app */;
			productType = "com.apple.product-type.application";
		};
		9F7DB5412E37DFDE00782FDA /* DashboardTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9F7DB5592E37DFDE00782FDA /* Build configuration list for PBXNativeTarget "DashboardTests" */;
			buildPhases = (
				9F7DB53E2E37DFDE00782FDA /* Sources */,
				9F7DB53F2E37DFDE00782FDA /* Frameworks */,
				9F7DB5402E37DFDE00782FDA /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9F7DB5442E37DFDE00782FDA /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				9F7DB5452E37DFDE00782FDA /* DashboardTests */,
			);
			name = DashboardTests;
			packageProductDependencies = (
			);
			productName = DashboardTests;
			productReference = 9F7DB5422E37DFDE00782FDA /* DashboardTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		9F7DB54B2E37DFDE00782FDA /* DashboardUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9F7DB55C2E37DFDE00782FDA /* Build configuration list for PBXNativeTarget "DashboardUITests" */;
			buildPhases = (
				9F7DB5482E37DFDE00782FDA /* Sources */,
				9F7DB5492E37DFDE00782FDA /* Frameworks */,
				9F7DB54A2E37DFDE00782FDA /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				9F7DB54E2E37DFDE00782FDA /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				9F7DB54F2E37DFDE00782FDA /* DashboardUITests */,
			);
			name = DashboardUITests;
			packageProductDependencies = (
			);
			productName = DashboardUITests;
			productReference = 9F7DB54C2E37DFDE00782FDA /* DashboardUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		9F7DB52C2E37DFDC00782FDA /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					9F7DB5332E37DFDC00782FDA = {
						CreatedOnToolsVersion = 16.4;
					};
					9F7DB5412E37DFDE00782FDA = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 9F7DB5332E37DFDC00782FDA;
					};
					9F7DB54B2E37DFDE00782FDA = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 9F7DB5332E37DFDC00782FDA;
					};
				};
			};
			buildConfigurationList = 9F7DB52F2E37DFDC00782FDA /* Build configuration list for PBXProject "Dashboard" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 9F7DB52B2E37DFDC00782FDA;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 9F7DB5352E37DFDC00782FDA /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				9F7DB5332E37DFDC00782FDA /* Dashboard */,
				9F7DB5412E37DFDE00782FDA /* DashboardTests */,
				9F7DB54B2E37DFDE00782FDA /* DashboardUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		9F7DB5322E37DFDC00782FDA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9F7DB5402E37DFDE00782FDA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9F7DB54A2E37DFDE00782FDA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		9F7DB5302E37DFDC00782FDA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9F7DB53E2E37DFDE00782FDA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9F7DB5482E37DFDE00782FDA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		9F7DB5442E37DFDE00782FDA /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9F7DB5332E37DFDC00782FDA /* Dashboard */;
			targetProxy = 9F7DB5432E37DFDE00782FDA /* PBXContainerItemProxy */;
		};
		9F7DB54E2E37DFDE00782FDA /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 9F7DB5332E37DFDC00782FDA /* Dashboard */;
			targetProxy = 9F7DB54D2E37DFDE00782FDA /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		9F7DB5542E37DFDE00782FDA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Dashboard/Info.plist;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		9F7DB5552E37DFDE00782FDA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Dashboard/Info.plist;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		9F7DB5572E37DFDE00782FDA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Dashboard/Dashboard.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSUIElement = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.universalco.Dashboard;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		9F7DB5582E37DFDE00782FDA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Dashboard/Dashboard.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSUIElement = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.universalco.Dashboard;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		9F7DB55A2E37DFDE00782FDA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.universalco.DashboardTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Dashboard.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Dashboard";
			};
			name = Debug;
		};
		9F7DB55B2E37DFDE00782FDA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.universalco.DashboardTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Dashboard.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Dashboard";
			};
			name = Release;
		};
		9F7DB55D2E37DFDE00782FDA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.universalco.DashboardUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = Dashboard;
			};
			name = Debug;
		};
		9F7DB55E2E37DFDE00782FDA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.universalco.DashboardUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = Dashboard;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		9F7DB52F2E37DFDC00782FDA /* Build configuration list for PBXProject "Dashboard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9F7DB5542E37DFDE00782FDA /* Debug */,
				9F7DB5552E37DFDE00782FDA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9F7DB5562E37DFDE00782FDA /* Build configuration list for PBXNativeTarget "Dashboard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9F7DB5572E37DFDE00782FDA /* Debug */,
				9F7DB5582E37DFDE00782FDA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9F7DB5592E37DFDE00782FDA /* Build configuration list for PBXNativeTarget "DashboardTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9F7DB55A2E37DFDE00782FDA /* Debug */,
				9F7DB55B2E37DFDE00782FDA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9F7DB55C2E37DFDE00782FDA /* Build configuration list for PBXNativeTarget "DashboardUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9F7DB55D2E37DFDE00782FDA /* Debug */,
				9F7DB55E2E37DFDE00782FDA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 9F7DB52C2E37DFDC00782FDA /* Project object */;
}
