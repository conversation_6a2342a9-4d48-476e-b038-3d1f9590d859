import SwiftUI

struct MenuView: View {
    @State private var panelController = PanelController()
    
    var body: some View {
        VStack {
            <PERSON><PERSON>("Show Panel") {
                panelController.showPanel()
            }
            
            <PERSON><PERSON>("Hide Panel") {
                panelController.hidePanel()
            }
            
            Divider()
            
            <PERSON><PERSON>("Quit") {
                NSApplication.shared.terminate(nil)
            }
            .keyboardShortcut("q")
        }
        .onAppear {
            panelController.showPanel()
        }
    }
}