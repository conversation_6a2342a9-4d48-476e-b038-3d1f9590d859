import SwiftUI
import Cocoa

class SettingsModel: ObservableObject {
    static let shared = SettingsModel()
    
    // Panel appearance settings
    @AppStorage("panelTitle") var panelTitle = "Dashboard Panel"
    @AppStorage("panelWidth") var panelWidth = 300.0
    @AppStorage("panelHeight") var panelHeight = 200.0
    @AppStorage("panelBackgroundColor") var panelBackgroundColorData = Data()
    @AppStorage("panelOpacity") var panelOpacity = 0.95
    
    // Panel positioning settings
    @AppStorage("panelPosition") var panelPosition = PanelPosition.topRight.rawValue
    @AppStorage("panelLevel") var panelLevel = PanelLevel.floating.rawValue
    @AppStorage("panelOffsetX") var panelOffsetX = 20.0
    @AppStorage("panelOffsetY") var panelOffsetY = 80.0
    
    // Content settings
    @AppStorage("showDateTime") var showDateTime = true
    @AppStorage("showSystemInfo") var showSystemInfo = true
    @AppStorage("customText") var customText = ""
    @AppStorage("showCustomText") var showCustomText = false
    
    // Window behavior settings
    @AppStorage("hideOnDeactivate") var hideOnDeactivate = false
    @AppStorage("alwaysOnTop") var alwaysOnTop = true
    @AppStorage("canJoinAllSpaces") var canJoinAllSpaces = true
    
    private init() {
        // Set default background color if none exists
        if panelBackgroundColorData.isEmpty {
            if let colorData = try? NSKeyedArchiver.archivedData(withRootObject: NSColor.controlBackgroundColor, requiringSecureCoding: false) {
                panelBackgroundColorData = colorData
            }
        }
    }
    
    var panelBackgroundColor: NSColor {
        get {
            if let color = try? NSKeyedUnarchiver.unarchivedObject(ofClass: NSColor.self, from: panelBackgroundColorData) {
                return color
            }
            return NSColor.controlBackgroundColor
        }
        set {
            if let colorData = try? NSKeyedArchiver.archivedData(withRootObject: newValue, requiringSecureCoding: false) {
                panelBackgroundColorData = colorData
            }
        }
    }
    
    var panelPositionEnum: PanelPosition {
        get { PanelPosition(rawValue: panelPosition) ?? .topRight }
        set { panelPosition = newValue.rawValue }
    }
    
    var panelLevelEnum: PanelLevel {
        get { PanelLevel(rawValue: panelLevel) ?? .floating }
        set { panelLevel = newValue.rawValue }
    }
}

enum PanelPosition: String, CaseIterable {
    case topLeft = "topLeft"
    case topRight = "topRight"
    case bottomLeft = "bottomLeft"
    case bottomRight = "bottomRight"
    case center = "center"
    case custom = "custom"
    
    var displayName: String {
        switch self {
        case .topLeft: return "Top Left"
        case .topRight: return "Top Right"
        case .bottomLeft: return "Bottom Left"
        case .bottomRight: return "Bottom Right"
        case .center: return "Center"
        case .custom: return "Custom"
        }
    }
}

enum PanelLevel: String, CaseIterable {
    case desktop = "desktop"           // Behind all windows (on wallpaper)
    case normal = "normal"             // Normal window level
    case floating = "floating"         // Above normal windows
    case modalPanel = "modalPanel"     // Above floating windows
    case mainMenu = "mainMenu"         // Highest level (above menu bar)
    
    var displayName: String {
        switch self {
        case .desktop: return "Desktop (Behind Windows)"
        case .normal: return "Normal"
        case .floating: return "Floating (Above Windows)"
        case .modalPanel: return "Modal Panel"
        case .mainMenu: return "Highest (Above Menu Bar)"
        }
    }
    
    var nsWindowLevel: NSWindow.Level {
        switch self {
        case .desktop: return NSWindow.Level(rawValue: Int(CGWindowLevelForKey(.desktopWindow)))
        case .normal: return .normal
        case .floating: return .floating
        case .modalPanel: return .modalPanel
        case .mainMenu: return NSWindow.Level(rawValue: Int(CGWindowLevelForKey(.mainMenuWindow)) + 1)
        }
    }
}
