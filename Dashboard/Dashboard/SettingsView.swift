import SwiftUI

struct SettingsView: View {
    @ObservedObject private var settings = SettingsModel.shared
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // Appearance Tab
            AppearanceSettingsView()
                .tabItem {
                    Image(systemName: "paintbrush")
                    Text("Appearance")
                }
                .tag(0)
            
            // Position Tab
            PositionSettingsView()
                .tabItem {
                    Image(systemName: "move.3d")
                    Text("Position")
                }
                .tag(1)
            
            // Content Tab
            ContentSettingsView()
                .tabItem {
                    Image(systemName: "doc.text")
                    Text("Content")
                }
                .tag(2)
            
            // Behavior Tab
            BehaviorSettingsView()
                .tabItem {
                    Image(systemName: "gear")
                    Text("Behavior")
                }
                .tag(3)
        }
        .frame(width: 500, height: 400)
    }
}

struct AppearanceSettingsView: View {
    @ObservedObject private var settings = SettingsModel.shared
    
    var body: some View {
        Form {
            Section("Panel Appearance") {
                HStack {
                    Text("Title:")
                    TextField("Panel Title", text: $settings.panelTitle)
                }
                
                HStack {
                    Text("Width:")
                    Slider(value: $settings.panelWidth, in: 200...800, step: 10)
                    Text("\(Int(settings.panelWidth))px")
                        .frame(width: 50, alignment: .trailing)
                }
                
                HStack {
                    Text("Height:")
                    Slider(value: $settings.panelHeight, in: 150...600, step: 10)
                    Text("\(Int(settings.panelHeight))px")
                        .frame(width: 50, alignment: .trailing)
                }
                
                HStack {
                    Text("Opacity:")
                    Slider(value: $settings.panelOpacity, in: 0.3...1.0, step: 0.05)
                    Text("\(Int(settings.panelOpacity * 100))%")
                        .frame(width: 40, alignment: .trailing)
                }
                
                HStack {
                    Text("Background Color:")
                    ColorPicker("", selection: Binding(
                        get: { Color(settings.panelBackgroundColor) },
                        set: { settings.panelBackgroundColor = NSColor($0) }
                    ))
                    .labelsHidden()
                }
            }
        }
        .padding()
    }
}

struct PositionSettingsView: View {
    @ObservedObject private var settings = SettingsModel.shared
    
    var body: some View {
        Form {
            Section("Panel Position") {
                HStack {
                    Text("Position:")
                    Picker("Position", selection: $settings.panelPositionEnum) {
                        ForEach(PanelPosition.allCases, id: \.self) { position in
                            Text(position.displayName).tag(position)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                HStack {
                    Text("Window Level:")
                    Picker("Level", selection: $settings.panelLevelEnum) {
                        ForEach(PanelLevel.allCases, id: \.self) { level in
                            Text(level.displayName).tag(level)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                }
                
                if settings.panelPositionEnum == .custom {
                    HStack {
                        Text("X Offset:")
                        TextField("X", value: $settings.panelOffsetX, format: .number)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .frame(width: 80)
                        Text("px from right")
                    }
                    
                    HStack {
                        Text("Y Offset:")
                        TextField("Y", value: $settings.panelOffsetY, format: .number)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .frame(width: 80)
                        Text("px from top")
                    }
                }
            }
        }
        .padding()
    }
}

struct ContentSettingsView: View {
    @ObservedObject private var settings = SettingsModel.shared
    
    var body: some View {
        Form {
            Section("Display Content") {
                Toggle("Show Date & Time", isOn: $settings.showDateTime)
                Toggle("Show System Information", isOn: $settings.showSystemInfo)
                Toggle("Show Custom Text", isOn: $settings.showCustomText)
                
                if settings.showCustomText {
                    VStack(alignment: .leading) {
                        Text("Custom Text:")
                        TextEditor(text: $settings.customText)
                            .frame(height: 80)
                            .border(Color.gray.opacity(0.3))
                    }
                }
            }
        }
        .padding()
    }
}

struct BehaviorSettingsView: View {
    @ObservedObject private var settings = SettingsModel.shared
    
    var body: some View {
        Form {
            Section("Window Behavior") {
                Toggle("Always on Top", isOn: $settings.alwaysOnTop)
                    .help("Keep panel above other windows")
                
                Toggle("Hide when App Deactivates", isOn: $settings.hideOnDeactivate)
                    .help("Hide panel when clicking outside the app")
                
                Toggle("Show on All Spaces", isOn: $settings.canJoinAllSpaces)
                    .help("Display panel on all virtual desktops")
            }
        }
        .padding()
    }
}

#Preview {
    SettingsView()
}
