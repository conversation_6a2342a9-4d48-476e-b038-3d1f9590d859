import SwiftUI
import Cocoa

class SettingsWindowController: NSWindowController {
    static let shared = SettingsWindowController()
    
    private override init(window: NSWindow?) {
        super.init(window: window)
        setupWindow()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private convenience init() {
        self.init(window: nil)
    }
    
    private func setupWindow() {
        let settingsView = SettingsView()
        let hostingView = NSHostingView(rootView: settingsView)
        
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 500, height: 400),
            styleMask: [.titled, .closable, .miniaturizable],
            backing: .buffered,
            defer: false
        )
        
        window.title = "Dashboard Settings"
        window.contentView = hostingView
        window.center()
        window.setFrameAutosaveName("SettingsWindow")
        window.isReleasedWhenClosed = false
        
        // Make window non-resizable for cleaner UI
        window.styleMask.remove(.resizable)
        
        self.window = window
    }
    
    func showSettings() {
        if window == nil {
            setupWindow()
        }
        
        window?.makeKeyAndOrderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
    }
    
    func hideSettings() {
        window?.orderOut(nil)
    }
}
