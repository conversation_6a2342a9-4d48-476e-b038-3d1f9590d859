import SwiftUI
import Cocoa

@Observable
class PanelController {
    private var panel: NSPanel?
    private let settings = SettingsModel.shared

    func showPanel() {
        if panel == nil {
            createPanel()
        } else {
            // Update panel properties if settings changed
            updatePanelProperties()
        }
        panel?.orderFront(nil)
        if settings.alwaysOnTop {
            NSApp.activate(ignoringOtherApps: true)
        }
    }

    func hidePanel() {
        panel?.orderOut(nil)
    }

    func refreshPanel() {
        if panel != nil {
            hidePanel()
            panel = nil
            showPanel()
        }
    }
    
    private func createPanel() {
        let contentView = PanelContentView()
        let hostingView = NSHostingView(rootView: contentView)

        guard let screen = NSScreen.main else { return }

        let panelSize = NSSize(width: settings.panelWidth, height: settings.panelHeight)
        let panelRect = calculatePanelRect(size: panelSize, screen: screen)

        panel = NSPanel(
            contentRect: panelRect,
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )

        updatePanelProperties()
        panel?.contentView = hostingView
    }

    private func updatePanelProperties() {
        guard let panel = panel else { return }

        panel.title = settings.panelTitle
        panel.level = settings.panelLevelEnum.nsWindowLevel
        panel.alphaValue = CGFloat(settings.panelOpacity)

        // Configure collection behavior
        var behavior: NSWindow.CollectionBehavior = []
        if settings.canJoinAllSpaces {
            behavior.insert(.canJoinAllSpaces)
        }
        if settings.alwaysOnTop {
            behavior.insert(.stationary)
        }
        panel.collectionBehavior = behavior

        // Configure hiding behavior
        panel.hidesOnDeactivate = settings.hideOnDeactivate

        // Update panel frame if position settings changed
        if let screen = NSScreen.main {
            let panelSize = NSSize(width: settings.panelWidth, height: settings.panelHeight)
            let newRect = calculatePanelRect(size: panelSize, screen: screen)
            panel.setFrame(newRect, display: true, animate: true)
        }
    }

    private func calculatePanelRect(size: NSSize, screen: NSScreen) -> NSRect {
        let screenFrame = screen.frame
        let position = settings.panelPositionEnum

        let x: CGFloat
        let y: CGFloat

        switch position {
        case .topLeft:
            x = screenFrame.minX + settings.panelOffsetX
            y = screenFrame.maxY - size.height - settings.panelOffsetY
        case .topRight:
            x = screenFrame.maxX - size.width - settings.panelOffsetX
            y = screenFrame.maxY - size.height - settings.panelOffsetY
        case .bottomLeft:
            x = screenFrame.minX + settings.panelOffsetX
            y = screenFrame.minY + settings.panelOffsetY
        case .bottomRight:
            x = screenFrame.maxX - size.width - settings.panelOffsetX
            y = screenFrame.minY + settings.panelOffsetY
        case .center:
            x = screenFrame.midX - size.width / 2
            y = screenFrame.midY - size.height / 2
        case .custom:
            x = screenFrame.maxX - size.width - settings.panelOffsetX
            y = screenFrame.maxY - size.height - settings.panelOffsetY
        }

        return NSRect(x: x, y: y, width: size.width, height: size.height)
    }
}