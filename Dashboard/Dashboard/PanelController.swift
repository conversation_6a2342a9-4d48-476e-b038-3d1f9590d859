import SwiftUI
import Cocoa

@Observable
class PanelController {
    private var panel: NSPanel?
    
    func showPanel() {
        if panel == nil {
            createPanel()
        }
        panel?.orderFront(nil)
        NSApp.activate(ignoringOtherApps: true)
    }
    
    func hidePanel() {
        panel?.orderOut(nil)
    }
    
    private func createPanel() {
        let contentView = PanelContentView()
        let hostingView = NSHostingView(rootView: contentView)
        
        guard let screen = NSScreen.main else { return }
        
        let panelSize = NSSize(width: 300, height: 200)
        let panelRect = NSRect(
            x: screen.frame.maxX - panelSize.width - 20,
            y: screen.frame.maxY - panelSize.height - 80,
            width: panelSize.width,
            height: panelSize.height
        )
        
        panel = NSPanel(
            contentRect: panelRect,
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )
        
        panel?.title = "Dashboard"
        panel?.contentView = hostingView
        panel?.level = .floating
        panel?.collectionBehavior = [.canJoinAllSpaces]
    }
}