import SwiftUI

struct PanelContentView: View {
    @ObservedObject private var settings = SettingsModel.shared
    @State private var currentTime = Date()
    @State private var timer: Timer?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                Text(settings.panelTitle)
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
                Button(action: openSettings) {
                    Image(systemName: "gear")
                        .foregroundColor(.secondary)
                }
                .buttonStyle(PlainButtonStyle())
                .help("Settings")
            }

            Divider()

            // Content based on settings
            VStack(alignment: .leading, spacing: 8) {
                if settings.showDateTime {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Current Time")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(currentTime, style: .time)
                            .font(.title2)
                            .fontWeight(.medium)
                        Text(currentTime, style: .date)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                if settings.showSystemInfo {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("System Info")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text("macOS \(ProcessInfo.processInfo.operatingSystemVersionString)")
                            .font(.caption)
                        Text("Memory: \(String(format: "%.1f GB", Double(ProcessInfo.processInfo.physicalMemory) / 1_073_741_824))")
                            .font(.caption)
                    }
                }

                if settings.showCustomText && !settings.customText.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Custom")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(settings.customText)
                            .font(.caption)
                    }
                }

                if !settings.showDateTime && !settings.showSystemInfo && (!settings.showCustomText || settings.customText.isEmpty) {
                    Text("Panel content can be customized in Settings")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .italic()
                }
            }

            Spacer()
        }
        .padding()
        .background(Color(settings.panelBackgroundColor))
        .opacity(settings.panelOpacity)
        .onAppear {
            startTimer()
        }
        .onDisappear {
            stopTimer()
        }
    }
    
    private func startTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            currentTime = Date()
        }
    }
    
    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }
    
    private func openSettings() {
        SettingsWindowController.shared.showSettings()
    }
}

#Preview {
    PanelContentView()
        .frame(width: 300, height: 200)
}
